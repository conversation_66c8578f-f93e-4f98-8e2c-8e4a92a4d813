/* Enhanced accessibility styles for MemberSearch component */

/* Ensure the select container has proper touch targets */
.upace-select__control {
  min-height: 60px !important;
  font-size: 18px !important;
  border-radius: 8px !important;
  cursor: text !important;
}

/* Larger input area for better accessibility */
.upace-select__input-container {
  padding: 8px 12px !important;
}

.upace-select__input {
  font-size: 18px !important;
  min-height: 40px !important;
  padding: 8px 0 !important;
}

/* Enhanced focus styles for better visibility */
.upace-select__control--is-focused {
  border: 3px solid #009DC4 !important;
  box-shadow: 0 0 0 1px #009DC4 !important;
}

/* Larger placeholder text */
.upace-select__placeholder {
  font-size: 18px !important;
  color: #666 !important;
}

/* Enhanced dropdown menu styling */
.upace-select__menu {
  font-size: 18px !important;
  z-index: 9999 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Larger option touch targets */
.upace-select__option {
  font-size: 18px !important;
  min-height: 50px !important;
  padding: 12px 16px !important;
  cursor: pointer !important;
}

.upace-select__option--is-focused {
  background-color: #f0f8ff !important;
  color: #333 !important;
}

.upace-select__option--is-selected {
  background-color: #009DC4 !important;
  color: white !important;
}

/* Enhanced single value display */
.upace-select__single-value {
  font-size: 18px !important;
  color: #333 !important;
}

/* Clear indicator styling */
.upace-select__clear-indicator {
  padding: 8px !important;
  cursor: pointer !important;
}

.upace-select__clear-indicator:hover {
  color: #009DC4 !important;
}

/* Dropdown indicator styling */
.upace-select__dropdown-indicator {
  padding: 8px !important;
  cursor: pointer !important;
}

/* Loading indicator */
.upace-select__loading-indicator {
  padding: 8px !important;
}

/* Value container */
.upace-select__value-container {
  padding: 8px 12px !important;
  min-height: 40px !important;
}

/* Indicators container */
.upace-select__indicators {
  padding-right: 8px !important;
}

/* Ensure proper spacing and touch targets on mobile devices */
@media (max-width: 768px) {
  .upace-select__control {
    min-height: 70px !important;
    font-size: 20px !important;
  }
  
  .upace-select__input {
    font-size: 20px !important;
    min-height: 45px !important;
  }
  
  .upace-select__placeholder {
    font-size: 20px !important;
  }
  
  .upace-select__menu {
    font-size: 20px !important;
  }
  
  .upace-select__option {
    font-size: 20px !important;
    min-height: 60px !important;
    padding: 16px 20px !important;
  }
  
  .upace-select__single-value {
    font-size: 20px !important;
  }
}

/* VoiceOver specific enhancements */
@media (prefers-reduced-motion: no-preference) {
  .upace-select__control {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .upace-select__control {
    border-width: 3px !important;
  }
  
  .upace-select__control--is-focused {
    border-width: 4px !important;
  }
  
  .upace-select__option--is-focused {
    background-color: #000 !important;
    color: #fff !important;
  }
}

/* Ensure keyboard navigation is visible */
.upace-select__option:focus {
  outline: 3px solid #009DC4 !important;
  outline-offset: -3px !important;
}

/* Loading state styling */
.upace-select__loading-message {
  font-size: 18px !important;
  padding: 12px 16px !important;
}

/* No options message styling */
.upace-select__no-options-message {
  font-size: 18px !important;
  padding: 12px 16px !important;
  color: #666 !important;
}
