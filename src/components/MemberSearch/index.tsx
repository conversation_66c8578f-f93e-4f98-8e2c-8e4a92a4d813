import AsyncSelect from "react-select/async";
import useMemberSearch from "../../hooks/useMemberSearch";

type TMemberSearchProps = {
  uniId: string;
};

function MemberSearch({ uniId }: TMemberSearchProps) {
  const { searchMembers, onMemberSelect } = useMemberSearch(uniId || "");

  return (
    <>
      <label id="member-search-label" className="sr-only">
        Search for a member by first or last name
      </label>
      <AsyncSelect
        isMulti={false}
        backspaceRemovesValue={false}
        isClearable
        cacheOptions
        defaultOptions
        onChange={onMemberSelect}
        loadOptions={searchMembers}
        classNamePrefix="upace-select"
        placeholder="Search by first name / last name"
        aria-labelledby="member-search-label"
        aria-describedby="member-search-instructions"
        inputId="member-search-input"
      />
      <div id="member-search-instructions" className="sr-only">
        Type a member's first or last name to search. Use arrow keys to navigate
        results and Enter to select.
      </div>
    </>
  );
}

export default MemberSearch;
