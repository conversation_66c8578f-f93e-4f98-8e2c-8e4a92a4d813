import AsyncSelect from "react-select/async";
import { components } from "react-select";
import useMemberSearch from "../../hooks/useMemberSearch";
import { useRef, useEffect } from "react";
import "./MemberSearch.css";

type TMemberSearchProps = {
  uniId: string;
};

function MemberSearch({ uniId }: TMemberSearchProps) {
  const { searchMembers, onMemberSelect } = useMemberSearch(uniId || "");
  const selectRef = useRef<any>(null);

  // Focus management for VoiceOver accessibility
  useEffect(() => {
    const handleFocus = () => {
      // Ensure keyboard appears when VoiceOver focuses on the input
      if (selectRef.current) {
        const inputElement = selectRef.current.inputRef;
        if (inputElement) {
          // Force focus and show keyboard for VoiceOver users
          inputElement.focus();
          inputElement.click();
        }
      }
    };

    // Listen for VoiceOver focus events
    const selectElement = document.getElementById("member-search-input");
    if (selectElement) {
      selectElement.addEventListener("focus", handleFocus);
      return () => {
        selectElement.removeEventListener("focus", handleFocus);
      };
    }
  }, []);

  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      minHeight: "60px", // Increased height for better touch accessibility
      fontSize: "18px", // Larger font size for better readability
      border: state.isFocused ? "3px solid #009DC4" : "2px solid #ccc",
      borderRadius: "8px",
      boxShadow: state.isFocused ? "0 0 0 1px #009DC4" : "none",
      "&:hover": {
        borderColor: "#009DC4",
      },
    }),
    input: (provided: any) => ({
      ...provided,
      fontSize: "18px",
      minHeight: "40px", // Ensure input area is large enough
      padding: "8px 0",
    }),
    placeholder: (provided: any) => ({
      ...provided,
      fontSize: "18px",
      color: "#666",
    }),
    singleValue: (provided: any) => ({
      ...provided,
      fontSize: "18px",
    }),
    menu: (provided: any) => ({
      ...provided,
      fontSize: "18px",
      zIndex: 9999,
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      fontSize: "18px",
      minHeight: "50px", // Larger touch targets for options
      padding: "12px 16px",
      backgroundColor: state.isSelected
        ? "#009DC4"
        : state.isFocused
        ? "#f0f8ff"
        : "white",
      color: state.isSelected ? "white" : "#333",
      "&:active": {
        backgroundColor: "#009DC4",
        color: "white",
      },
    }),
  };

  return (
    <>
      <label id="member-search-label" className="sr-only">
        Search for a member by first or last name
      </label>
      <AsyncSelect
        ref={selectRef}
        isMulti={false}
        backspaceRemovesValue={false}
        isClearable
        cacheOptions
        defaultOptions
        onChange={onMemberSelect}
        loadOptions={searchMembers}
        classNamePrefix="upace-select"
        placeholder="Search by first name / last name"
        aria-labelledby="member-search-label"
        aria-describedby="member-search-instructions"
        inputId="member-search-input"
        styles={customStyles}
        menuPortalTarget={document.body}
        menuPosition="fixed"
        // Enhanced accessibility props
        aria-live="polite"
        aria-expanded={false}
        tabSelectsValue={false}
        openMenuOnFocus={true}
        // Ensure input is accessible to screen readers
        components={{
          Input: (props: any) => (
            <components.Input
              {...props}
              aria-label="Search for a member by first or last name"
              aria-describedby="member-search-instructions"
              autoComplete="off"
              inputMode="text"
            />
          ),
        }}
      />
      <div id="member-search-instructions" className="sr-only">
        Type a member's first or last name to search. Use arrow keys to navigate
        results and Enter to select. The search will begin automatically as you
        type.
      </div>
    </>
  );
}

export default MemberSearch;
