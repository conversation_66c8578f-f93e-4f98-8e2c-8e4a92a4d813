import {
  Box,
  Button,
  Modal,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  Text,
  useDisclosure,
} from "@chakra-ui/react";
import { useRef } from "react";
import { LiaCogSolid } from "react-icons/lia";
import FacilitySelect from "../../pages/Home__deprecated/components/FacilitySelect";
import { useSelector } from "react-redux";
import { RootState } from "../../reduxtoolkit/store";
import { getFacilitySelectorBehaviour } from "./utils/selector";

const allFacilityValue = { name: "All Facilities", id: 0 };

function FacilitySelectorButton() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const finalRef = useRef(null);

  const { clientConfig, selectedFacility } = useSelector(
    (state: RootState) => state.configSlice
  );

  //  Construct facility list for selection
  const uniIdKey = clientConfig ? Object.keys(clientConfig)[0] : 0;
  const selectedClientConfig = clientConfig?.[uniIdKey];
  const facilityList = [...(selectedClientConfig?.facilities || [])];
  facilityList.unshift(allFacilityValue);

  let activeFacility = selectedFacility?.[uniIdKey];
  if (!activeFacility) {
    activeFacility = allFacilityValue;
  }

  const facilitySelectorBehaviour = getFacilitySelectorBehaviour();

  return (
    <Box bottom="20px" {...facilitySelectorBehaviour.positionProps}>
      <Button
        variant="unstyled"
        onClick={onOpen}
        aria-label="Change facility"
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        gap={1} // Adjusted gap slightly, can be fine-tuned
        p={2} // Add some padding for better click area if needed
        height="auto" // Ensure button height adjusts to content
        color={facilitySelectorBehaviour.color} // Apply color to button for children to inherit
      >
        <LiaCogSolid
          color={facilitySelectorBehaviour.color}
          fontSize="24px"
          aria-hidden="true"
        />
        <Text
          color={facilitySelectorBehaviour.color}
          fontSize="xs"
          fontWeight={"600"}
        >
          Change Facility
        </Text>
      </Button>

      <Modal
        finalFocusRef={finalRef}
        isOpen={isOpen}
        onClose={onClose}
        aria-labelledby="facility-selector-modal-title"
        returnFocusOnClose={true}
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader id="facility-selector-modal-title">
            Select a facility
          </ModalHeader>
          <ModalCloseButton aria-label="Close facility selector" />
          <ModalBody>
            {selectedClientConfig && uniIdKey && (
              <FacilitySelect
                universityId={uniIdKey}
                facility={activeFacility}
                facilities={facilityList}
              />
            )}
          </ModalBody>

          <ModalFooter>
            <Button
              colorScheme="gray"
              mr={3}
              onClick={onClose}
              aria-label="Close facility selector"
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}

export default FacilitySelectorButton;
